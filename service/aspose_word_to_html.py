import os
import uuid
import re
from datetime import datetime
import sys
import requests
from flask import current_app
# 初始化Python.NET
# import pythonnet
# import clr

# # 当使用较新版本的pythonnet时，尝试获取运行时状态
# try:
#     # 适用于pythonnet 3.0+
#     from pythonnet import get_runtime, set_runtime
    
#     # 检查运行时是否已加载
#     runtime = None
#     try:
#         runtime = get_runtime()
#     except RuntimeError:
#         pass
        
#     # 如果运行时尚未加载，则设置为coreclr
#     if runtime is None:
#         try:
#             set_runtime(runtime="coreclr")
#         except RuntimeError:
#             # 运行时可能已经通过其他方式加载
#             pass
# except ImportError:
#     # 适用于较旧的pythonnet版本
#     pass

# # 加载Aspose.Words DLL
# script_dir = os.path.dirname(os.path.abspath(__file__))
# dll_path = os.path.join(script_dir, r'Aspose.Words.23.8.0.dll')
# if os.path.exists(dll_path):
#     clr.AddReference(dll_path)
# else:
#     raise FileNotFoundError(f"找不到Aspose.Words DLL文件: {dll_path}")

# # 引用System库
# clr.AddReference("System")
# clr.AddReference("System.IO")

# # 导入Aspose.Words命名空间
# from Aspose.Words import Document, License
# from Aspose.Words.Saving import HtmlSaveOptions, CssStyleSheetType
# # 导入System命名空间
# from System import Array, Byte
# from System.IO import MemoryStream, SeekOrigin

# 设置Aspose日志级别为DEBUG
import logging
logging.getLogger('aspose').setLevel(logging.DEBUG)  # 设置为DEBUG级别以允许所有日志

class AsposeWordToHtmlConverter:
    """使用Aspose.Words库将Word文档转换为HTML的转换器"""
    
    def __init__(self):
        """初始化转换器并设置图片保存目录"""
        # self.image_dir = 'static/images/aspose'
        # # 确保图片目录存在
        # os.makedirs(self.image_dir, exist_ok=True)
        
        # # 检查license文件是否存在 - 使用多个可能的位置
        # license_path = './key.txt'
        # # 尝试绝对路径
        # if not os.path.exists(license_path):
        #     script_dir = os.path.dirname(os.path.abspath(__file__))
        #     license_path = os.path.join(script_dir, 'key.txt')
            
            
        # print(f"尝试加载license文件: {license_path}")
        # if os.path.exists(license_path):
        #     try:
        #         # 加载license
        #         license = License()
        #         # 正确的方式加载license
        #         license.SetLicense(license_path)
        #         print("Aspose.Words License加载成功!")
                
        #         # 验证license是否生效
        #         from Aspose.Words import VersionInfo
        #         print(f"Aspose.Words版本: {VersionInfo.Product}, 是否已加载license: {not VersionInfo.IsLicensed}")
                
        #     except Exception as e:
        #         print(f"警告: 加载license文件失败: {str(e)}")
        # else:
        #     print(f"警告: License文件不存在，将使用评估模式: {license_path}")
        print("初始化完成")
    
    def convert_to_html(self, ins):
        """将Word文档转换为HTML
        Args:
            ins: Word文档文件对象（字节流）
        Returns:
            tuple: (HTML字符串, 生成的HTML文件路径)
        """
        # try:
            # 将BytesIO对象内容转换为字节数组，然后直接加载到Document
        #     ins.seek(0)
        #     bytes_data = ins.read()
            
        #     # 使用.NET MemoryStream加载文档
        #     from System.IO import MemoryStream
        #     from System import Array, Byte
            
        #     # 创建.NET字节数组
        #     buffer = Array[Byte](bytes_data)
        #     # 创建.NET内存流
        #     mem_stream = MemoryStream(buffer)
            
        #     # 检查文档字节大小
        #     print(f"Word文档大小: {len(bytes_data)}字节")
        #     if len(bytes_data) == 0:
        #         print("错误: Word文档内容为空")
        #         raise ValueError("输入的Word文档内容为空")
                
        #     try:
        #         # 从内存流加载文档
        #         doc = Document(mem_stream)
        #         # 导入NodeType枚举
        #         from Aspose.Words import NodeType
        #         print(f"文档加载成功，页数: {doc.PageCount}，段落数: {doc.GetChildNodes(NodeType.Any, True).Count}")
        #         if doc.PageCount == 0 or doc.GetChildNodes(NodeType.Any, True).Count == 0:
        #             print("警告: 文档似乎是空的或没有内容")
                
        #         # 主动移除所有页眉页脚
        #         for section in doc.Sections:
        #             # 移除所有页眉
        #             if section.HeadersFooters:
        #                 headers_to_remove = []
        #                 for header_footer in section.HeadersFooters:
        #                     # 收集所有页眉
        #                     if "Header" in str(header_footer.HeaderFooterType):
        #                         headers_to_remove.append(header_footer)
                        
        #                 # 移除收集到的页眉
        #                 for header in headers_to_remove:
        #                     try:
        #                         section.HeadersFooters.Remove(header)
        #                     except Exception as e:
        #                         print(f"移除页眉时出错: {str(e)}")
                
        #                 # 收集所有页脚
        #                 footers_to_remove = []
        #                 for header_footer in section.HeadersFooters:
        #                     if "Footer" in str(header_footer.HeaderFooterType):
        #                         footers_to_remove.append(header_footer)
                        
        #                 # 移除收集到的页脚
        #                 for footer in footers_to_remove:
        #                     try:
        #                         section.HeadersFooters.Remove(footer)
        #                     except Exception as e:
        #                         print(f"移除页脚时出错: {str(e)}")
                
        #         # 创建唯一的输出目录来保存图片
        #         timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        #         output_dir = os.path.join(self.image_dir, f"{timestamp}_{uuid.uuid4().hex}")
        #         os.makedirs(output_dir, exist_ok=True)
                
        #         # 配置保存选项
        #         save_options = HtmlSaveOptions()
        #         # 设置CSS样式表嵌入到HTML文件中
        #         # save_options.CssStyleSheetType = CssStyleSheetType.Embedded
        #         # 启用漂亮的格式化输出
        #         save_options.PrettyFormat = True
        #         # 将图片以Base64格式嵌入到HTML中
        #         save_options.ExportImagesAsBase64 = True
                
        #         # 禁用导出页眉和页脚
        #         try:
        #             # 尝试直接设置属性值，使用正确的枚举格式
        #             from Aspose.Words.Saving import ExportHeadersFootersMode
        #             # 使用枚举值而不是整数，避免使用Python保留关键字None
        #             # 使用枚举的第一个值（通常是0，对应不导出）
        #             save_options.ExportHeadersFootersMode = ExportHeadersFootersMode.FirstSectionHeaderLastSectionFooter
        #         except Exception as e:
        #             print(f"设置ExportHeadersFootersMode失败: {str(e)}")
        #             # 尝试替代方法
        #             try:
        #                 # 某些版本可能使用这些属性
        #                 if hasattr(save_options, 'ExportPageHeaders'):
        #                     save_options.ExportPageHeaders = False
        #                 if hasattr(save_options, 'ExportPageFooters'):
        #                     save_options.ExportPageFooters = False
        #             except Exception as e2:
        #                 print(f"禁用页眉页脚尝试2失败: {str(e2)}")
                
        #         # 禁用导出页码、标题等文档信息
        #         save_options.ExportDocumentProperties = False

                
        #         temp_html_path = os.path.join(output_dir, f"temp_{timestamp}.html")
        #         doc.Save(temp_html_path, save_options)

        #         # 读取Aspose生成的HTML内容
        #         with open(temp_html_path, 'r', encoding='utf-8') as html_file:
        #             html_content = html_file.read()
                
        #         # 标记占位符
        #         html_content = self._mark_placeholders(html_content)
                
        #         # 将处理后的HTML内容写回文件
        #         with open(temp_html_path, 'w', encoding='utf-8') as html_file:
        #             html_file.write(html_content)
               
        #         return html_content
        #     finally:
        #         # 确保释放.NET资源
        #         if mem_stream:
        #             mem_stream.Close()
        #             mem_stream.Dispose()
            
        # except Exception as e:
        #     print(f"转换Word文档到HTML时出错: {str(e)}")
        #     raise
        
        # 调用接口返回
        url = current_app.config['WORD_TO_HTML_API_URL']
        # url = "http://localhost:20119/report/formTemplate/wordToHtml"
        # 上传文件
        files = {
            'file': ('document.docx', ins, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
        }
        
        try:
            response = requests.post(url, files=files)
            response.raise_for_status()  # 检查响应状态
            
            response_data = response.json()
            if response_data and 'data' in response_data:
                html_content = response_data['data']
                if html_content:
                    return self._mark_placeholders(html_content)
                else:
                    logging.error("API返回的HTML内容为空")
                    return None
            else:
                logging.error(f"API返回格式错误: {response_data}")
                return None
        except Exception as e:
            logging.error(f"调用Word转HTML API时出错: {str(e)}")
            return None
    
    def _mark_placeholders(self, html_content):
        """标记HTML中的占位符并插入高亮样式
        Args:
            html_content: 需要处理的HTML内容字符串
        Returns:
            str: 处理后的HTML内容，包含高亮样式的占位符
        """
        # 检查HTML内容是否为空
        if not html_content:
            logging.warning("_mark_placeholders收到空HTML内容")
            return html_content
            
        try:
            # 查找形如 {{xxx}} 的占位符并添加特殊样式
            placeholder_pattern = r'\{\{(.*?)\}\}'
            original_count = len(re.findall(placeholder_pattern, html_content))
            logging.debug(f"找到{original_count}个占位符待处理")
            
            html_content = re.sub(
                placeholder_pattern,
                lambda m: f'<span class="placeholder">{m.group(0)}</span>',
                html_content
            )
            
            # 插入增强的CSS样式到<head>标签前
            style = (
                "<style>"
                ".placeholder {"
                "background-color: #ffeb3b;"
                "color: #d32f2f;"
                "font-weight: bold;"
                "border-radius: 4px;"
                "padding: 2px 6px;"
                "margin: 0 2px;"
                "display: inline-block;"
                "box-shadow: 0 1px 3px rgba(0,0,0,0.12);"
                "transition: all 0.3s ease;"
                "cursor: pointer;"
                "}"
                ".placeholder:hover {"
                "background-color: #ffd740;"
                "box-shadow: 0 2px 5px rgba(0,0,0,0.2);"
                "transform: translateY(-1px);"
                "}"
                "</style>"
            )
            
            if "<head>" in html_content:
                html_content = html_content.replace("<head>", f"<head>{style}", 1)
                logging.debug("样式已插入到<head>标签中")
            else:
                html_content = style + html_content
                logging.debug("未找到<head>标签，样式已添加到文档开头")
            
            processed_count = len(re.findall(r'class="placeholder"', html_content))
            logging.info(f"占位符处理完成：成功处理{processed_count}个占位符")
            
            return html_content
            
        except Exception as e:
            logging.error(f"处理占位符时出错: {str(e)}")
            # 返回原始内容，确保不会因为占位符处理失败而导致整个转换失败
            return html_content
    