import json
from io import BytesIO
from docx import Document
import requests
from flask import current_app

# 保存结构化数据到JSON文件
def save_hierarchy_to_json(hierarchy, output_file):
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(hierarchy, f, ensure_ascii=False, indent=4)

# 上传文件到服务器
def upload_file(file):
    # 请求上传文件的地址
    url = current_app.config['UPLOAD_API_URL']
    
    # 创建文件对象
    files = {
        'file': ('image.png', file, 'image/png')
    }
    
    # 发送请求 - 不需要手动设置 Content-Type，requests 会自动处理
    response = requests.post(url, files=files)
    if response.json()['code'] != 200:
        raise Exception(response.json()['msg'])
    print(response.json())
    # 返回URL {'code': 200, 'msg': '操作成功', 'data': {'ossId': '1920382624279306241', 'url': '/home/<USER>', 'fileName': 'image.png'}}
    return response.json()['data']['url']



# 生成占位符
def convert_placeholders_stream(ins):
    """
    将Word文档中的填空处（如下划线）转换为poi-tl格式的占位符，key从1开始递增，并保留下划线格式
    一个连续的下划线只生成一个占位符
    :param ins: 输入Word文件流
    :return: 输出Word文件流
    """
    # 打开Word文档
    doc = Document(ins)
    
    # 计数器，用于生成从1开始的key
    counter = 1
    
    # 跟踪所有替换的占位符，用于调试
    replaced_placeholders = []
    
    def add_placeholder(run=None, original_text="", underline=True, paragraph=None):
        """统一处理占位符添加逻辑"""
        nonlocal counter
        
        placeholder = f"{{{{{counter}}}}}"
        
        # 如果提供了run，更新run文本
        if run:
            run.text = placeholder
            if underline:
                run.underline = True
        # 如果提供了paragraph，添加新run
        elif paragraph:
            new_run = paragraph.add_run(placeholder)
            if underline:
                new_run.underline = True
                
        # 计数器加1
        counter += 1
        return placeholder
    
    # 已废弃
    def process_runs(runs, is_table_cell=False):
        """处理一组runs，识别并替换为占位符"""
        nonlocal counter
        
        # 如果是表格单元格且runs为空或长度为0，直接返回None
        if is_table_cell and (not runs or len(runs) == 0):
            return None
            
        i = 0
        while i < len(runs):
            run = runs[i]
            
            # 1. 处理下划线情况
            if run.underline and (not run.text.strip() or run.text.strip() == "_"):
                # 如果前一个run也有下划线，跳过这个run（避免重复处理连续下划线）
                if i > 0 and runs[i-1].underline:
                    i += 1
                    continue
                add_placeholder(run=run, original_text=run.text)
            
            # 2. 处理冒号/等号后添加占位符
            elif "：" in run.text or ":" in run.text or "=" in run.text:
                import re
                
                original_text = run.text
                processed_text = original_text
                
                # 匹配冒号或等号后面有空格或等号后面没有字符串
                colon_pattern = r'[：:=]\s+|=\s*(?=$)'
                
                # 找出所有匹配的冒号/等号（后面有空格或到行末）
                matches = list(re.finditer(colon_pattern, original_text))
                
                # 统计匹配数量
                match_count = len(matches)
                
                if match_count > 0:
                    # 从后向前处理每个匹配，避免位置变化
                    for match in reversed(matches):
                        colon_char = match.group()[0]  # 获取冒号/等号字符（第一个字符）
                        colon_end = match.start() + 1  # 冒号/等号的结束位置
                        
                        # 检查冒号/等号后面是否已经有占位符
                        remaining_text = processed_text[colon_end:]
                        if re.match(r'^\s*\{\{\d+\}\}', remaining_text):
                            continue  # 如果已经有占位符，跳过
                        
                        # 生成占位符
                        placeholder = f"{{{{{counter}}}}}"
                        
                        # 在冒号/等号后插入占位符
                        processed_text = processed_text[:colon_end] + placeholder + processed_text[colon_end:]
                        
                        # 计数器加1
                        counter += 1
                
                # 更新run文本
                if processed_text != original_text:
                    run.text = processed_text
            
            i += 1
        return None
    
    def process_paragraph(paragraph, is_table_cell=False, add_underline=True):
        """处理一组runs，识别并替换为占位符"""
        nonlocal counter
        
        runs = paragraph.runs

        # 如果是表格单元格且runs为空或长度为0，直接返回None
        if is_table_cell and (not runs or len(runs) == 0):
            return None
        
        # 1. 处理下划线情况
        i = 0
        while i < len(runs):
            run = runs[i]

            if run.underline and (not run.text.strip() or run.text.strip() == "_"):
                # 如果前一个run也有下划线，跳过这个run（避免重复处理连续下划线）
                if i > 0 and runs[i-1].underline:
                    i += 1
                    continue
                add_placeholder(run=run, original_text=run.text, underline=add_underline)
            i += 1

        # 2. 处理冒号/等号后添加占位符    
        if "：" in paragraph.text or ":" in paragraph.text or "=" in paragraph.text:
                import re
                
                original_text = paragraph.text
                processed_text = original_text
                
                # 匹配冒号或等号后面有空格或等号后面没有字符串
                colon_pattern = r'[：:=]\s+|=\s*(?=$)'
                
                # 找出所有匹配的冒号/等号（后面有空格或到行末）
                matches = list(re.finditer(colon_pattern, original_text))
                
                # 统计匹配数量
                match_count = len(matches)
                
                if match_count > 0:
                    # 从后向前处理每个匹配，避免位置变化
                    for match in reversed(matches):
                        colon_char = match.group()[0]  # 获取冒号/等号字符（第一个字符）
                        colon_end = match.start() + 1  # 冒号/等号的结束位置
                        
                        # 检查冒号/等号后面是否已经有占位符
                        remaining_text = processed_text[colon_end:]
                        if re.match(r'^\s*\{\{\d+\}\}', remaining_text):
                            continue  # 如果已经有占位符，跳过
                        
                        # 生成占位符
                        placeholder = f"{{{{{counter}}}}}"
                        
                        # 在冒号/等号后插入占位符
                        processed_text = processed_text[:colon_end] + placeholder + processed_text[colon_end:]
                        
                        # 计数器加1
                        counter += 1
                
                # 更新paragraph文本
                if processed_text != original_text:
                    paragraph.text = processed_text

        return None
        
    
    def process_cell(cell):
        """处理单个单元格"""
        nonlocal counter
        
        # 判断是否有多个段落（只有多个段落时才添加下划线）
        has_multiple_paragraphs = len(cell.paragraphs) > 1
        
        # 情况1: 空单元格或只有一个空段落的单元格
        if len(cell.paragraphs) == 1 and not cell.paragraphs[0].text.strip():
            paragraph = cell.paragraphs[0]
            add_placeholder(paragraph=paragraph, underline=has_multiple_paragraphs)
        # 情况2: 有内容的单元格
        else:
            # 处理每个段落
            for paragraph in cell.paragraphs:
                if paragraph.text.strip():  # 跳过空段落
                    process_paragraph(paragraph, is_table_cell=True, add_underline=has_multiple_paragraphs)
            
            # 处理嵌套表格
            for nested_table in cell.tables:
                process_table(nested_table)
    
    def process_table(table):
        """处理整个表格"""
        for row in table.rows:
            for cell in row.cells:
                process_cell(cell)
    
    # 1. 处理所有段落
    for paragraph in doc.paragraphs:
        # 跳过空白段落
        if paragraph.text.strip():
            process_paragraph(paragraph)
    
    # 2. 处理所有表格
    for table in doc.tables:
        process_table(table)
    
    # 创建输出流并保存
    output_stream = BytesIO()
    doc.save(output_stream)
    output_stream.seek(0)
    return output_stream

# os  = convert_placeholders_stream(BytesIO(open("/Users/<USER>/Documents/工作/广安项目/word2vform/service/抗震试验记录 .docx", "rb").read()))
# # 保存到文件
# with open("test_placeholders.docx", "wb") as f:
#     f.write(os.read())

