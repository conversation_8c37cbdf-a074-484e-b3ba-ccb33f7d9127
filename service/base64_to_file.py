import base64
import re
import io
import logging
from urllib.parse import urlparse

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("Base64Converter")

def base64_to_file_stream(base64_str, remove_prefix=True):
    """
    将 Base64 字符串转换为 BytesIO 文件流

    Args:
        base64_str (str): 包含 Base64 数据的字符串（可能包含前缀）
        remove_prefix (bool): 是否移除前缀（如 'data:image/png;base64,'）

    Returns:
        io.BytesIO: 包含解码后二进制数据的文件流

    Raises:
        ValueError: 输入字符串不是有效的 Base64 编码
    """
    # 如果输入为None，返回None
    if base64_str is None:
        return None
        
    # 移除前缀（如果需要）
    if remove_prefix and isinstance(base64_str, str):
        # 匹配 data URI 前缀（如 "data:image/png;base64,"）
        match = re.match(r'data:.*?base64,(.*)', base64_str)
        if match:
            base64_str = match.group(1)

    # 解码 Base64 字符串
    try:
        binary_data = base64.b64decode(base64_str)
    except Exception as e:
        logger.error(f"Invalid Base64 string: {str(e)}")
        return None

    # 创建 BytesIO 流
    file_stream = io.BytesIO(binary_data)
    return file_stream