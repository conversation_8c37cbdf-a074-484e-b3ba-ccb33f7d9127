import re
import logging
from lxml.html import fromstring
from service.enhanced_table_parser import EnhancedTableParser

# 配置日志
logger = logging.getLogger("HtmlToVFormConverter")


class HtmlToVFormConverter:
    def __init__(self):
        # 统一使用单一计数器，将在initialize_counters_from_html中根据实际占位符更新
        self.counter = 1
        # 将自己作为父转换器传递给表格解析器，实现计数器共享
        self.table_parser = EnhancedTableParser(parent_converter=self)

    # 为了向后兼容，提供id_counter属性
    @property
    def id_counter(self):
        """获取id计数器值（向后兼容）"""
        return self.counter
    
    @id_counter.setter
    def id_counter(self, value):
        """设置id计数器值（向后兼容）"""
        self.counter = value
    
    def extract_max_placeholder_value_from_html(self, html_tree):
        """从整个HTML文档中提取最大的占位符值
        
        Args:
            html_tree: HTML树
            
        Returns:
            int: 最大的占位符数值
        """
        # 使用table_parser的方法来提取最大占位符值
        return self.table_parser.extract_max_placeholder_value(html_tree)
    
    def initialize_counters_from_html(self, html_tree):
        """根据HTML中的占位符初始化计数器
        
        Args:
            html_tree: HTML树
        """
        max_placeholder = self.extract_max_placeholder_value_from_html(html_tree)
        if max_placeholder > 1:  # 只有当找到有效占位符时才更新
            # 统一使用占位符值作为起始计数器，不再使用大基数
            self.counter = max_placeholder
            logger.info(f"HtmlToVFormConverter: 从HTML中提取到的最大占位符值: {max_placeholder}, 已初始化共享计数器")
    
    def generate_id(self):
        """生成唯一id"""
        self.counter += 1
        return self.counter
    
    def convert(self, html_content):
        """将HTML转换为vForm JSON
        Args:
            html_content: HTML内容字符串
        Returns:
            dict: vForm JSON对象
        """
        # 解析HTML
        if isinstance(html_content, str):
            html_tree = fromstring(html_content)
        else:
            html_tree = html_content
        
        # 在开始转换前，根据HTML中的占位符初始化计数器
        self.initialize_counters_from_html(html_tree)
        
        # 创建vForm根对象 - 使用与VFormConverter相同的结构（暂不设置currentId）
        form_config = {
            "modelName": "formData",
            "refName": "vForm",
            "rulesName": "rules",
            "labelWidth": 80, 
            "labelPosition": "top",
            "size": "",
            "labelAlign": "label-left-align",
            "cssCode": "",
            "customClass": [],
            "functions": "",
            "layoutType": "PC",
            "jsonVersion": 3,
            "dataSources": [],
            "onFormCreated": "",
            "onFormMounted": "",
            "onFormDataChange": "",
            "onFormValidate": ""
        }
        
        # 初始化组件列表
        widget_list = []
        
        # 转换HTML为vForm组件
        body = html_tree.find('.//body')
        if body is not None:
            self._process_body_content(body, widget_list)
        
        # 在所有组件生成完毕后，设置currentId为当前计数器值
        form_config["currentId"] = self.counter
        
        # 返回与VFormConverter相同结构的JSON
        return {
            "widgetList": widget_list,
            "formConfig": form_config
        }
    
    def _process_body_content(self, element, items_list):
        """处理HTML元素及其所有子元素，按顺序转换为vForm组件
        
        Args:
            element: HTML元素
            items_list: 存放生成组件的列表
        """
        # 使用xpath获取所有直接子节点，严格按照DOM树顺序
        # 这将包括文本节点、元素节点等所有类型的节点
        child_nodes = element.xpath('node()')
        
        # 逐个处理子节点
        for node in child_nodes:
            # 检查节点类型，这里我们主要处理元素节点
            if hasattr(node, 'tag'):  # 元素节点
                if node.tag in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                    vform_item = self.convert_heading(node)
                    items_list.append(vform_item)
                elif node.tag == 'p':
                    vform_items = self.convert_paragraph(node)
                    items_list.extend(vform_items)
                elif node.tag == 'table':
                    # 使用增强的表格解析器
                    vform_item = self.table_parser.parse_table(node)
                    items_list.append(vform_item)
                elif node.tag == 'div':
                    # 处理div容器，递归处理其内容
                    self._process_body_content(node, items_list)
                elif node.tag == 'ul' or node.tag == 'ol':
                    # 处理列表
                    list_items = self.convert_list(node)
                    items_list.extend(list_items)
                elif node.tag == 'img':
                    # 处理独立图片
                    src = node.get('src', '')
                    if src:
                        img_item = self.table_parser._create_image(src, node)
                        items_list.append(img_item)
                else:
                    # 递归处理未知元素的子内容
                    self._process_body_content(node, items_list)
            # 可以添加对文本节点等其他类型节点的处理，如果有必要
    
    def convert_heading(self, element):
        """转换标题元素
        Args:
            element: HTML标题元素
        Returns:
            dict: vForm标题组件
        """
        level = int(element.tag[1])
        text = self.get_element_text(element)
        
        # 根据标题级别调整字体大小
        font_size = {
            1: '24px',
            2: '20px',
            3: '18px',
            4: '16px',
            5: '14px',
            6: '13px'
        }.get(level, '13px')
        
        # 获取标题样式
        style = element.get('style', '')
        text_align = 'left'  # 默认左对齐
        
        # 解析对齐方式
        if 'text-align:' in style:
            align_match = re.search(r'text-align:\s*([\w-]+)', style)
            if align_match:
                text_align = align_match.group(1)
        
        heading_id = self.generate_id()
        return {
            "key": heading_id,
            "type": "static-text",
            "icon": "static-text",
            "formItemFlag": False,
            "options": {
                "name": f"static-text_{heading_id}",
                "columnWidth": "200px",
                "hidden": False,
                "textContent": text,
                "textAlign": text_align,
                "fontSize": font_size,
                "fontWeight": "bold",
                "preWrap": True,
                "customClass": [],
                "onCreated": "",
                "onMounted": "",
                "label": "static-text"
            },
            "id": f"statictext_{self.generate_id()}"
        }
    
    def convert_paragraph(self, element):
        """转换段落元素
        Args:
            element: HTML段落元素
        Returns:
            list: vForm组件列表
        """
        results = []
        
        # 获取段落对齐方式
        style = element.get('style', '')
        text_align = 'left'
        if 'text-align:' in style:
            align_match = re.search(r'text-align:\s*([\w-]+)', style)
            if align_match:
                text_align = align_match.group(1)
        
        # 获取背景色
        background_color = ''
        if 'background-color:' in style:
            bg_match = re.search(r'background-color:\s*(#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}|rgba?\([^)]+\)|[a-zA-Z]+)', style)
            if bg_match:
                background_color = bg_match.group(1)
        
        # 提取字体样式
        font_size = "13px"  # 默认字体大小
        font_weight = "normal"  # 默认字体粗细
        
        # 检查是否有字体大小
        if 'font-size:' in style:
            size_match = re.search(r'font-size:\s*(\d+\.?\d*)(pt|px)', style)
            if size_match:
                size_value = float(size_match.group(1))
                size_unit = size_match.group(2)
                if size_unit == 'pt':
                    # 将pt转换为px (1pt ≈ 1.33px)
                    size_value = size_value * 1.33
                font_size = f"{size_value}px"
                
        # 检查是否加粗
        if 'font-weight:' in style:
            weight_match = re.search(r'font-weight:\s*(bold|bolder|\d+)', style)
            if weight_match:
                font_weight = weight_match.group(1)
        
        # 检查是否包含占位符
        placeholder_spans = element.xpath('.//span[@class="placeholder"]')
        if placeholder_spans:
            for span in placeholder_spans:
                placeholder_text = span.text_content() or ''  # 使用text_content()获取所有文本
                # 从占位符中提取变量名
                var_match = re.search(r'\{\{\s*(.+?)\s*\}\}', placeholder_text)
                if var_match:
                    var_name = var_match.group(1)
                    # 创建input组件，传递完整的占位符文本用于提取currentId
                    input_item = self.table_parser._create_input(placeholder_text)
                    # 设置变量名
                    input_item["options"]["name"] = var_name
                    # 设置对齐方式
                    input_item["options"]["labelAlign"] = text_align
                    results.append(input_item)
        else:
            # 对于不包含占位符的文本，获取整个段落的内容
            text = self.get_element_text(element)
            
            # 检查是否包含复选框符号□
            if '□' in text:
                # 尝试检测复选框模式
                checkbox_items = self.table_parser._process_checkbox_text(text)
                if checkbox_items:
                    results.extend(checkbox_items)
                    return results
            
            if text.strip():
                # 创建静态文本组件
                text_id = self.generate_id()
                text_item = {
                    "key": text_id,
                    "type": "static-text",
                    "icon": "static-text",
                    "formItemFlag": False,
                    "options": {
                        "name": f"statictext_{text_id}",
                        "columnWidth": "200px",
                        "hidden": False,
                        "textContent": text,
                        "textAlign": text_align,
                        "fontSize": font_size,
                        "fontWeight": font_weight,
                        "backgroundColor": background_color,
                        "preWrap": True,
                        "customClass": [],
                        "onCreated": "",
                        "onMounted": "",
                        "label": "static-text"
                    },
                    "id": f"statictext_{self.generate_id()}"
                }
                results.append(text_item)
        
        # 处理图片 - 这部分处理独立段落中的图片
        # 注意：表格中的段落图片由表格解析器单独处理
        images = element.xpath('.//img')
        for img in images:
            src = img.get('src', '')
            if src:
                img_item = self.table_parser._create_image(src, img)
                results.append(img_item)
        
        return results
    
    def _get_formatted_text(self, element):
        """获取格式化文本，处理空白和换行，但保留有意义的格式
        
        Args:
            element: HTML元素
            
        Returns:
            str: 格式化的文本
        """
        # 获取元素的直接文本内容
        text = element.text or ""
        
        # 处理子元素
        for child in element:
            # 根据子元素标签处理空格策略
            if child.tag in ['span', 'strong', 'em', 'b', 'i']:
                child_text = child.text_content()
                # 为行内元素添加内容时不添加额外空格
                text += child_text
            else:
                # 其他元素默认添加空格分隔
                child_text = child.text_content()
                if text and not text.endswith(' ') and child_text and not child_text.startswith(' '):
                    text += ' '
                text += child_text
            
            # 添加子元素的尾部文本
            if child.tail:
                text += child.tail
        
        # 清理并格式化文本
        # 1. 将连续多个换行替换为单个换行
        text = re.sub(r'\n{2,}', '\n', text)
        # 2. 将连续多个空格替换为单个空格
        text = re.sub(r' {2,}', ' ', text)
        # 3. 去除首尾空白
        text = text.strip()
        
        return text
    
    def convert_list(self, element):
        """转换列表元素
        Args:
            element: HTML列表元素(ul或ol)
        Returns:
            list: vForm组件列表
        """
        results = []
        
        # 确定列表类型
        list_type = "• " if element.tag == 'ul' else ""  # ul使用圆点，ol使用数字（由vForm处理）
        
        # 处理每个列表项
        for i, li in enumerate(element.xpath('./li')):
            # 获取列表项文本
            text = self.get_element_text(li)
            
            # 为有序列表添加编号
            prefix = f"{i+1}. " if element.tag == 'ol' else list_type
            
            # 创建静态文本组件
            list_item_id = self.generate_id()
            text_item = {
                "key": list_item_id,
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": False,
                "options": {
                    "name": f"statictext_{list_item_id}",
                    "columnWidth": "200px",
                    "hidden": False,
                    "textContent": f"{prefix}{text}",
                    "textAlign": "left",
                    "fontSize": "13px",
                    "preWrap": True,
                    "marginLeft": "20px",  # 添加左边距
                    "customClass": [],
                    "onCreated": "",
                    "onMounted": "",
                    "label": "static-text"
                },
                "id": f"statictext_{list_item_id}"
            }
            results.append(text_item)
        
        return results
    
    def get_element_text(self, element):
        """获取元素的所有文本内容包括子元素
        Args:
            element: HTML元素
        Returns:
            str: 元素的全部文本
        """
        text = element.text_content()
        # 处理文本中的多余空白和换行
        # 1. 将连续的多个空格替换为单个空格
        # 2. 将连续的多个换行替换为单个换行
        # 3. 去除首尾的空白字符
        normalized_text = re.sub(r'\s+', ' ', text).strip()
        return normalized_text
