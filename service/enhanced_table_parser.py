import re
from lxml.html import fromstring
from lxml import etree, html
import logging
from service.read_word import upload_file
from service.base64_to_file import base64_to_file_stream

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("TableParser")

class EnhancedTableParser:
    """增强的HTML表格解析器，特别处理合并单元格"""
    
    def __init__(self, parent_converter=None):
        """初始化表格解析器
        
        Args:
            parent_converter: 父转换器实例，用于共享计数器
        """
        if parent_converter is not None:
            # 如果有父转换器，引用其计数器
            self.parent_converter = parent_converter
            self._use_parent_counters = True
        else:
            # 独立使用时，维护自己的计数器
            self.parent_converter = None
            self._use_parent_counters = False
            self._counter = 1
    
    @property
    def counter(self):
        """获取计数器值"""
        if self._use_parent_counters:
            return self.parent_converter.counter
        else:
            return self._counter
    
    @counter.setter
    def counter(self, value):
        """设置计数器值"""
        if self._use_parent_counters:
            self.parent_converter.counter = value
        else:
            self._counter = value

    # 为了向后兼容，提供id_counter属性
    @property
    def id_counter(self):
        """获取id计数器值（向后兼容）"""
        return self.counter
    
    @id_counter.setter
    def id_counter(self, value):
        """设置id计数器值（向后兼容）"""
        self.counter = value
    
    def extract_max_placeholder_value(self, html_element):
        """从HTML元素中提取所有占位符的最大数值
        
        Args:
            html_element: HTML元素
            
        Returns:
            int: 最大的占位符数值，如果没有找到则返回1
        """
        max_value = 1
        
        # 递归搜索所有占位符
        def search_placeholders(element):
            nonlocal max_value
            
            # 如果元素为空，直接返回
            if element is None:
                return
            
            # 检查当前元素的文本内容
            if hasattr(element, 'text') and element.text:
                matches = re.findall(r'\{\{\s*(\d+)\s*\}\}', element.text)
                for match in matches:
                    max_value = max(max_value, int(match))
            
            # 检查尾部文本
            if hasattr(element, 'tail') and element.tail:
                matches = re.findall(r'\{\{\s*(\d+)\s*\}\}', element.tail)
                for match in matches:
                    max_value = max(max_value, int(match))
            
            # 递归检查所有子元素
            if hasattr(element, '__iter__'):
                for child in element:
                    if child is not None:
                        search_placeholders(child)
        
        # 开始搜索
        search_placeholders(html_element)
        
        return max_value
    
    def initialize_counters_from_html(self, html_element):
        """根据HTML中的占位符初始化计数器
        
        Args:
            html_element: HTML元素
        """
        max_placeholder = self.extract_max_placeholder_value(html_element)
        self.counter = max_placeholder
        logger.info(f"从HTML中提取到的最大占位符值: {max_placeholder}, 已初始化计数器")
    
    def generate_id(self):
        """生成唯一id"""
        if self._use_parent_counters:
            self.parent_converter.counter += 1
            return self.parent_converter.counter
        else:
            self._counter += 1
            return self._counter
        
    def parse_table(self, html_table):
        """解析HTML表格元素，支持合并单元格
        
        Args:
            html_table: HTML表格元素或HTML表格字符串
        
        Returns:
            dict: VForm格式的表格JSON对象
        """
        # 如果输入是字符串，将其解析为HTML元素
        if isinstance(html_table, str):
            # 尝试直接解析，如果不是完整的HTML，则包装它
            try:
                html_element = fromstring(html_table)
                if html_element.tag != 'table':
                    table_elements = html_element.xpath('//table')
                    if table_elements:
                        html_table = table_elements[0]
                    else:
                        html_table = fromstring(f"<table>{html_table}</table>")
                else:
                    html_table = html_element
            except Exception as e:
                logger.error(f"解析HTML表格时出错: {str(e)}")
                html_table = fromstring(f"<table>{html_table}</table>")
        
        # 在解析表格之前，根据HTML中的占位符初始化计数器
        # 但只有在独立使用时才初始化，使用共享计数器时跳过
        if not self._use_parent_counters:
            self.initialize_counters_from_html(html_table)
        
        # 获取当前表格的直接行，不包括嵌套表格中的行
        # 使用更精确的XPath，只选择当前表格的直接子行
        rows = html_table.xpath('./thead/tr | ./tbody/tr | ./tr')
        if not rows:
            logger.warning("表格中未找到行")
            return self._create_empty_table()
        
        # 第一步: 分析表格结构，创建表格的物理布局矩阵
        # 初始化表格信息
        row_count = len(rows)
        
        # 保存原始表格的样式
        table_style = html_table.get('style', '')
        table_class = html_table.get('class', '')
        
        # 确定表格的最大物理列数（考虑colspan）
        max_cols = 0
        for row in rows:
            # 计算当前行的所有单元格占据的总列数
            cols_in_row = 0
            for cell in row.xpath('./td | ./th'):
                colspan = int(cell.get('colspan', 1))
                cols_in_row += colspan
            max_cols = max(max_cols, cols_in_row)
        
        logger.info(f"表格尺寸: {row_count}行 x {max_cols}列")
        
        # 创建物理单元格矩阵: 用来跟踪已经被单元格覆盖的位置
        # 当开始处理某行时，需要检查每个位置是否已经被上方单元格的rowspan占据
        # -1表示位置可用，(r, c)表示位置被第r行第c列的单元格占据
        cell_matrix = [[-1 for _ in range(max_cols)] for _ in range(row_count)]
        
        # 第二步: 扫描表格并填充矩阵
        for r, row in enumerate(rows):
            # logger.info(f"处理第{r+1}行")
            
            # 保存行的样式
            row_style = row.get('style', '')
            row_class = row.get('class', '')
            
            # 物理列索引，考虑前面单元格的colspan
            c = 0
            
            for cell in row.xpath('./td | ./th'):
                # 找到当前行的下一个可用列位置
                while c < max_cols and cell_matrix[r][c] != -1:
                    c += 1
                
                if c >= max_cols:
                    logger.warning(f"第{r+1}行超出了表格的最大列数 {max_cols}")
                    break
                
                # 获取colspan和rowspan
                colspan = int(cell.get('colspan', 1))
                rowspan = int(cell.get('rowspan', 1))
                
                # 记录单元格在矩阵中的覆盖范围
                for i in range(rowspan):
                    if r + i >= row_count:
                        continue  # 超出表格行数，忽略多余的rowspan
                    
                    for j in range(colspan):
                        if c + j >= max_cols:
                            continue  # 超出表格列数，忽略多余的colspan
                        
                        cell_matrix[r + i][c + j] = (r, c)  # 记录此位置被哪个单元格占据
                
                # 移动到下一个列位置
                c += colspan
        
        # 第三步: 创建VForm表格结构
        table_id = self.generate_id()
        table_node = {
            "key": table_id,
            "type": "table",
            "category": "container",
            "icon": "table",
            "rows": [],
            "options": {
                "name": f"table-{table_id}",
                "hidden": False,
                "tableWidth": "100%",  # 设置表格宽度为100%
                "tableLayout": "auto",  # 添加表格布局方式
                "customClass": []
            },
            "id": f"table-{table_id}"
        }
        
        # 处理表格样式
        if table_style:
            # 解析表格宽度
            width_match = re.search(r'width:\s*(\d+)(%|px|pt)', table_style)
            if width_match:
                table_node["options"]["tableWidth"] = width_match.group(0)
                
            # 添加原始样式类
            if table_class:
                table_node["options"]["customClass"].append(table_class)
        
        # 为每一行创建VForm行结构
        for r in range(row_count):
            row = rows[r]
            
            # 获取行高
            row_height = ""
            if row.get('style'):
                height_match = re.search(r'height:\s*(\d+\.?\d*)pt', row.get('style', ''))
                if height_match:
                    row_height = f"{float(height_match.group(1))}pt"
            
            row_id = self.generate_id()
            vform_row = {
                "type": "table-row",
                "category": "container", 
                "icon": "table-row",
                "internal": True,
                "merged": False,
                "cols": [],
                "options": {
                    "rowHeight": row_height,  # 设置行高
                    "customClass": []
                },
                "id": f"table-row-{row_id}"
            }
            
            # 处理这一行中的每个单元格
            c = 0
            while c < max_cols:
                cell_info = cell_matrix[r][c]
                
                if cell_info == -1:
                    # 这个位置没有单元格，可能是表格结构错误
                    logger.warning(f"位置 ({r+1}, {c+1}) 没有单元格覆盖，跳过")
                    c += 1
                    continue
                
                owner_row, owner_col = cell_info
                
                if owner_row == r and owner_col == c:
                    # 这是一个主单元格（不是被合并的占位符）
                    cell = rows[r].xpath('./td | ./th')[self._get_cell_index(cell_matrix, r, c)]
                    
                    # 获取colspan和rowspan
                    colspan = int(cell.get('colspan', 1))
                    rowspan = int(cell.get('rowspan', 1))
                    
                    # 获取单元格样式
                    cell_style = cell.get('style', '')
                    cell_align = 'left'  # 默认对齐方式
                    cell_width = ""
                    cell_height = ""
                    
                    # 解析单元格对齐方式
                    if 'text-align:' in cell_style:
                        align_match = re.search(r'text-align:\s*([\w-]+)', cell_style)
                        if align_match:
                            cell_align = align_match.group(1)
                    
                    # 解析单元格宽度
                    if 'width:' in cell_style:
                        width_match = re.search(r'width:\s*(\d+\.?\d*)(pt|px|%)', cell_style)
                        if width_match:
                            cell_width = f"{width_match.group(1)}{width_match.group(2)}"
                    
                    # 创建vform单元格
                    cell_id = self.generate_id()
                    vform_cell = {
                        "type": "table-cell",
                        "category": "container",
                        "icon": "table-cell",
                        "internal": True,
                        "widgetList": self._parse_cell_content(cell),
                        "merged": False,
                        "options": {
                            "name": f"table-cell-{cell_id}",
                            "cellWidth": cell_width,
                            "cellHeight": cell_height,
                            "colspan": colspan,
                            "rowspan": rowspan,
                            "wordBreak": False,
                            "textAlign": cell_align,  # 设置文本对齐方式
                            "verticalAlign": "middle",  # 设置垂直对齐方式
                            "customClass": []
                        },
                        "id": f"table-cell-{cell_id}"
                    }
                    
                    # 判断是否是表头单元格
                    if cell.tag == 'th':
                        vform_cell["options"]["isHeader"] = True
                        vform_cell["options"]["fontWeight"] = "bold"
                    
                    vform_row["cols"].append(vform_cell)
                    c += colspan
                elif owner_row < r:
                    # 这是被rowspan覆盖的位置，是垂直合并产生的占位符
                    # 检查水平合并情况
                    next_col = c + 1
                    colspan = 1
                    while next_col < max_cols and cell_matrix[r][next_col] == cell_matrix[r][c]:
                        colspan += 1
                        next_col = c + colspan
                    
                    # 创建被合并的占位单元格
                    merged_cell_id = self.generate_id()
                    merged_cell = {
                        "type": "table-cell",
                        "category": "container",
                        "icon": "table-cell",
                        "internal": True,
                        "widgetList": [],  # 被合并单元格内容为空
                        "merged": True,    # 标记为已合并
                        "options": {
                            "name": f"table-cell-{merged_cell_id}",
                            "cellWidth": "",
                            "cellHeight": "",
                            "colspan": colspan,
                            "rowspan": 1,
                            "wordBreak": False,
                            "customClass": []
                        },
                        "id": f"table-cell-{merged_cell_id}"
                    }
                    
                    vform_row["cols"].append(merged_cell)
                    c += colspan
                else:
                    # 不可能的情况，应该不会发生
                    logger.error(f"无法确定位置 ({r+1}, {c+1}) 的单元格类型")
                    c += 1
            
            # 将完成的行添加到表格
            table_node["rows"].append(vform_row)
        
        # 第四步: 验证表格结构
        merged_cell_count = 0
        merging_cell_count = 0
        
        for row in table_node["rows"]:
            for cell in row["cols"]:
                if cell.get("merged", False):
                    merged_cell_count += 1
                elif (cell["options"].get("rowspan", 1) > 1 or 
                      cell["options"].get("colspan", 1) > 1):
                    merging_cell_count += 1
        
        # logger.info(f"表格中包含 {merged_cell_count} 个被合并的占位单元格")
        # logger.info(f"表格中包含 {merging_cell_count} 个合并行为的主单元格")
        
        return table_node
    
    def _get_cell_index(self, cell_matrix, row, col):
        """获取物理单元格在行中的索引
        
        Args:
            cell_matrix: 单元格矩阵
            row: 行索引
            col: 列索引
            
        Returns:
            int: 单元格在行中的索引
        """
        cell_index = 0
        for c in range(col):
            if cell_matrix[row][c] != -1:
                owner_row, owner_col = cell_matrix[row][c]
                if owner_row == row and owner_col not in [cell_matrix[row][c2][1] for c2 in range(c)]:
                    cell_index += 1
        
        return cell_index
    
    def _parse_cell_content(self, cell):
        """解析单元格内容，按HTML结构顺序处理元素
        
        Args:
            cell: HTML单元格元素
            
        Returns:
            list: 单元格内容的vForm组件列表
        """
        widgets = []
        
        # 获取单元格中的样式信息
        style = cell.get('style', '')
        text_align = 'center'  # 默认居中对齐
        
        # 解析对齐方式
        if 'text-align:' in style:
            align_match = re.search(r'text-align:\s*([\w-]+)', style)
            if align_match:
                text_align = align_match.group(1)
        
        # 按HTML结构顺序处理单元格的所有子元素
        children = list(cell)  # 获取所有直接子元素
        
        if children:
            # 有子元素，按DOM顺序处理
            for child in children:
                child_widgets = self._parse_element_by_type(child, text_align)
                widgets.extend(child_widgets)
        else:
            # 没有子元素但可能有直接文本内容
            cell_text = self._get_plain_text(cell)
            if cell_text.strip():
                # 检查文本中是否包含复选框符号□
                if '□' in cell_text:
                    checkbox_widgets = self._process_checkbox_text(cell_text, text_align)
                    if checkbox_widgets:
                        widgets.extend(checkbox_widgets)
                        return widgets
                
                # 如果没有复选框或无法处理，创建静态文本
                text_id = self.generate_id()
                text_item = {
                    "key": text_id,
                    "type": "static-text",
                    "icon": "static-text",
                    "formItemFlag": False,
                    "options": {
                        "name": f"statictext_{text_id}",
                        "columnWidth": "200px",
                        "hidden": False,
                        "textContent": cell_text.strip(),
                        "textAlign": text_align,
                        "fontSize": "13px",
                        "preWrap": True,
                        "customClass": [],
                        "onCreated": "",
                        "onMounted": "",
                        "label": "static-text"
                    },
                    "id": f"statictext_{text_id}"
                }
                widgets.append(text_item)
        
        return widgets
    
    def _parse_element_by_type(self, element, text_align='center'):
        """根据元素类型解析HTML元素
        
        Args:
            element: HTML元素
            text_align: 文本对齐方式
            
        Returns:
            list: vForm组件列表
        """
        widgets = []
        tag = element.tag.lower()
        
        if tag == 'p':
            # 段落元素
            p_widgets = self._parse_paragraph(element, text_align)
            widgets.extend(p_widgets)
        elif tag == 'img':
            # 图片元素
            src = element.get('src', '')
            if src:
                img_item = self._create_image(src, element)
                widgets.append(img_item)
        elif tag == 'div':
            # div容器，递归处理其子元素
            div_widgets = self._parse_div_element(element, text_align)
            widgets.extend(div_widgets)
        elif tag == 'table':
            # 嵌套表格，递归解析
            nested_table = self.parse_table(element)
            if nested_table:
                widgets.append(nested_table)
        elif tag == 'span':
            # span元素
            span_widgets = self._parse_span_element(element, text_align)
            widgets.extend(span_widgets)
        elif tag in ['br', 'hr']:
            # 换行或分隔线元素，暂时忽略或转换为空格
            pass    
        else:
            # 其他元素，尝试提取文本内容
            element_text = self._get_plain_text(element)
            if element_text.strip():
                text_id = self.generate_id()
                text_item = {
                    "key": text_id,
                    "type": "static-text",
                    "icon": "static-text",
                    "formItemFlag": False,
                    "options": {
                        "name": f"statictext_{text_id}",
                        "columnWidth": "200px",
                        "hidden": False,
                        "textContent": element_text.strip(),
                        "textAlign": text_align,
                        "fontSize": "13px",
                        "preWrap": True,
                        "customClass": [],
                        "onCreated": "",
                        "onMounted": "",
                        "label": "static-text"
                    },
                    "id": f"statictext_{text_id}"
                }
                widgets.append(text_item)
        
        return widgets
    
    def _parse_div_element(self, div_element, text_align='center'):
        """解析div元素，按HTML结构顺序处理其子元素
        
        Args:
            div_element: HTML div元素
            text_align: 文本对齐方式
            
        Returns:
            list: vForm组件列表
        """
        widgets = []
        
        # 获取div的样式
        style = div_element.get('style', '')
        if 'text-align:' in style:
            align_match = re.search(r'text-align:\s*([\w-]+)', style)
            if align_match:
                text_align = align_match.group(1)
        
        # 按HTML结构顺序处理div的所有子元素
        children = list(div_element)
        
        if children:
            # 有子元素，按DOM顺序递归处理
            for child in children:
                child_widgets = self._parse_element_by_type(child, text_align)
                widgets.extend(child_widgets)
        else:
            # 没有子元素，检查是否有直接文本内容
            div_text = self._get_plain_text(div_element)
            if div_text.strip():
                # 检查文本中是否包含复选框符号□
                if '□' in div_text:
                    checkbox_widgets = self._process_checkbox_text(div_text, text_align)
                    if checkbox_widgets:
                        widgets.extend(checkbox_widgets)
                        return widgets
                
                # 创建静态文本
                text_id = self.generate_id()
                text_item = {
                    "key": text_id,
                    "type": "static-text",
                    "icon": "static-text",
                    "formItemFlag": False,
                    "options": {
                        "name": f"statictext_{text_id}",
                        "columnWidth": "200px",
                        "hidden": False,
                        "textContent": div_text.strip(),
                        "textAlign": text_align,
                        "fontSize": "13px",
                        "preWrap": True,
                        "customClass": [],
                        "onCreated": "",
                        "onMounted": "",
                        "label": "static-text"
                    },
                    "id": f"statictext_{text_id}"
                }
                widgets.append(text_item)
        
        return widgets
    
    def _parse_span_element(self, span_element, text_align='center'):
        """解析span元素
        
        Args:
            span_element: HTML span元素
            text_align: 文本对齐方式
            
        Returns:
            list: vForm组件列表
        """
        widgets = []
        
        # 获取span的类和文本内容
        span_class = span_element.get('class', '')
        span_text = self._get_plain_text(span_element)
        
        if span_class == 'placeholder' and span_text.strip():
            # 这是一个占位符span，提取变量名
            var_match = re.search(r'\{\{\s*(.+?)\s*\}\}', span_text)
            if var_match:
                var_name = var_match.group(1)
                # 创建输入组件，传递完整的占位符文本用于提取currentId
                input_item = self._create_input(span_text.strip())  # 传递完整的占位符文本
                input_item["options"]["name"] = var_name  # 设置name为变量名
                input_item["options"]["labelAlign"] = text_align
                widgets.append(input_item)
        elif span_text.strip():
            # 普通span，创建静态文本
            text_id = self.generate_id()
            text_item = {
                "key": text_id,
                "type": "static-text",
                "icon": "static-text",
                "formItemFlag": False,
                "options": {
                    "name": f"statictext_{text_id}",
                    "columnWidth": "200px",
                    "hidden": False,
                    "textContent": span_text.strip(),
                    "textAlign": text_align,
                    "fontSize": "13px",
                    "preWrap": True,
                    "customClass": [],
                    "onCreated": "",
                    "onMounted": "",
                    "label": "static-text"
                },
                "id": f"statictext_{text_id}"
            }
            widgets.append(text_item)
        
        return widgets
    
    
    def _get_plain_text(self, element):
        """获取元素的纯文本内容，保留必要的空白并进行格式化
        
        Args:
            element: HTML元素
            
        Returns:
            str: 元素的纯文本内容
        """
        # 获取元素的直接文本内容
        text = element.text or ""
        
        # 获取所有子元素的文本内容
        for child in element.iterchildren():
            # 添加子元素的文本
            if child.text:
                text += child.text
            # 添加子元素的尾部文本
            if child.tail:
                text += child.tail
        
        # 文本格式化：清理多余的换行符和空格，使文本更紧凑
        # 1. 将连续多个空白字符（包括换行、空格、制表符）替换为单个空格
        text = re.sub(r'\s+', ' ', text)
        # 2. 去除首尾空白
        text = text.strip()
                
        return text
    
    def _parse_paragraph(self, element, default_align='left'):
        """解析段落元素
        
        Args:
            element: HTML段落元素
            default_align: 默认对齐方式
            
        Returns:
            list: vForm组件列表
        """
        results = []
        
        # 获取段落对齐方式
        style = element.get('style', '')
        text_align = default_align
        if 'text-align:' in style:
            align_match = re.search(r'text-align:\s*([\w-]+)', style)
            if align_match:
                text_align = align_match.group(1)
        
        # 获取背景色
        background_color = ''
        if 'background-color:' in style:
            bg_match = re.search(r'background-color:\s*(#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}|rgba?\([^)]+\)|[a-zA-Z]+)', style)
            if bg_match:
                background_color = bg_match.group(1)
        
        # 查找段落中所有占位符及其位置
        placeholder_spans = element.xpath('.//span[@class="placeholder"]')
        
        if placeholder_spans:
            # 将段落转换为字符串以便于后续操作
            element_html = etree.tostring(element, encoding='unicode')
            
            # 收集所有占位符元素及其在HTML中的位置
            placeholders_with_positions = []
            
            for span in placeholder_spans:
                placeholder_text = span.text_content().strip()
                var_match = re.search(r'\{\{\s*(.+?)\s*\}\}', placeholder_text)
                if var_match:
                    var_name = var_match.group(1)
                    
                    # 获取该占位符在HTML中的XPath
                    span_xpath = element.getroottree().getpath(span)
                    
                    # 获取占位符在HTML中的位置
                    span_str = f'<span class="placeholder">{placeholder_text}</span>'
                    position = element_html.find(span_str)
                    
                    if position >= 0:
                        placeholders_with_positions.append({
                            'span': span,
                            'var_name': var_name,
                            'position': position,
                            'html_length': len(span_str),
                            'xpath': span_xpath
                        })
            
            # 按照在HTML中的位置排序占位符
            placeholders_with_positions.sort(key=lambda x: x['position'])
            
            # 如果有多个占位符，创建栅格布局，每个占位符独占一列
            if len(placeholders_with_positions) > 1:
                # 计算每列的span值（总共24列，平均分配）
                col_span = 24 // len(placeholders_with_positions)
                remaining_span = 24 % len(placeholders_with_positions)
                
                # 创建栅格布局
                grid_id = self.generate_id()
                grid = {
                    "key": grid_id,
                    "type": "grid",
                    "alias": "multi-input-grid",
                    "category": "container",
                    "icon": "grid",
                    "cols": [],
                    "options": {
                        "name": f"grid_{grid_id}",
                        "hidden": False,
                        "gutter": 12,
                        "colHeight": None,
                        "customClass": []
                    },
                    "id": f"grid_{grid_id}"
                }
                
                # 为每个占位符创建独立的列
                for i, placeholder in enumerate(placeholders_with_positions):
                    var_name = placeholder['var_name']
                    current_span = placeholder['span']
                    
                    label_text = ""
                    
                    # 如果不是第一个占位符，则尝试从前一个占位符结束位置到当前占位符开始位置提取标签文本
                    if i > 0:
                        prev_placeholder = placeholders_with_positions[i-1]
                        prev_end_pos = prev_placeholder['position'] + prev_placeholder['html_length']
                        current_start_pos = placeholder['position']
                        
                        # 提取两个占位符之间的HTML
                        between_html = element_html[prev_end_pos:current_start_pos]
                        
                        # 方法1：直接提取占位符前面的所有span内容
                        try:
                            # 创建临时元素来解析HTML
                            temp_element = fromstring(f"<div>{between_html}</div>")
                            
                            # 获取所有文本内容（包括span内的文本）
                            all_text = temp_element.text_content()
                            if all_text:
                                # 清理文本：去除占位符标记和多余空白
                                label_text = re.sub(r'\{\{[^}]*\}\}', '', all_text).strip()
                                
                                # 如果得到了有效的标签文本，使用它
                                if label_text and label_text != "":
                                    # 保持原有格式，不添加额外的标点
                                    pass
                                else:
                                    label_text = ""
                        except Exception as e:
                            logger.debug(f"解析标签文本时出错: {str(e)}")
                        
                        # 方法2：如果方法1失败，使用简单的文本提取
                        if not label_text:
                            # 直接提取HTML中的所有文本（去除标签）
                            text_only = re.sub(r'<[^>]+>', '', between_html)
                            if text_only:
                                label_text = re.sub(r'\{\{[^}]*\}\}', '', text_only).strip()
                    
                    # 如果是第一个占位符或无法获取前面的标签，尝试特殊处理
                    if not label_text:
                        # 对于第一个占位符，需要特别处理
                        if i == 0:
                            # 尝试从段落开始到第一个占位符之间提取文本
                            first_placeholder_pos = placeholder['position']
                            before_first_placeholder = element_html[:first_placeholder_pos]
                            
                            # 方法1：直接提取占位符前面的所有span内容
                            try:
                                # 创建临时元素来解析HTML
                                temp_element = fromstring(f"<div>{before_first_placeholder}</div>")
                                
                                # 获取所有文本内容（包括span内的文本）
                                all_text = temp_element.text_content()
                                if all_text:
                                    # 清理文本：去除占位符标记和多余空白
                                    label_text = re.sub(r'\{\{[^}]*\}\}', '', all_text).strip()
                                    
                                    # 如果得到了有效的标签文本，使用它
                                    if label_text and label_text != "":
                                        # 保持原有格式，不添加额外的标点
                                        pass
                                    else:
                                        label_text = ""
                            except Exception as e:
                                logger.debug(f"解析标签文本时出错: {str(e)}")
                            
                            # 方法2：如果方法1失败，使用简单的文本提取
                            if not label_text:
                                # 直接提取HTML中的所有文本（去除标签）
                                text_only = re.sub(r'<[^>]+>', '', before_first_placeholder)
                                if text_only:
                                    label_text = re.sub(r'\{\{[^}]*\}\}', '', text_only).strip()
                    
                    # 创建input组件，传递完整的占位符文本用于提取currentId
                    placeholder_text = placeholder['span'].text_content().strip()
                    input_item = self._create_input(placeholder_text, label_text)
                    # 设置变量名
                    input_item["options"]["name"] = var_name
                    # 设置对齐方式
                    input_item["options"]["labelAlign"] = text_align
                    
                    # 计算当前列的span值
                    current_col_span = col_span
                    if i < remaining_span:  # 将剩余的span分配给前几列
                        current_col_span += 1
                    
                    # 创建独立的栅格列
                    grid_col_id = self.generate_id()
                    grid_col = {
                        "type": "grid-col",
                        "category": "container",
                        "icon": "grid-col",
                        "internal": True,
                        "widgetList": [input_item],
                        "options": {
                            "name": f"gridCol_{grid_col_id}",
                            "hidden": False,
                            "border": False,
                            "span": current_col_span,
                            "offset": 0,
                            "push": 0,
                            "pull": 0,
                            "responsive": False,
                            "customClass": []
                        },
                        "id": f"grid-col-{grid_col_id}"
                    }
                    
                    grid["cols"].append(grid_col)
                
                results.append(grid)
            else:
                # 只有一个占位符，按原来的方式处理
                for i, placeholder in enumerate(placeholders_with_positions):
                    var_name = placeholder['var_name']
                    current_span = placeholder['span']
                    
                    label_text = ""
                    
                    # 如果不是第一个占位符，则尝试从前一个占位符结束位置到当前占位符开始位置提取标签文本
                    if i > 0:
                        prev_placeholder = placeholders_with_positions[i-1]
                        prev_end_pos = prev_placeholder['position'] + prev_placeholder['html_length']
                        current_start_pos = placeholder['position']
                        
                        # 提取两个占位符之间的HTML
                        between_html = element_html[prev_end_pos:current_start_pos]
                        
                        # 方法1：直接提取占位符前面的所有span内容
                        try:
                            # 创建临时元素来解析HTML
                            temp_element = fromstring(f"<div>{between_html}</div>")
                            
                            # 获取所有文本内容（包括span内的文本）
                            all_text = temp_element.text_content()
                            if all_text:
                                # 清理文本：去除占位符标记和多余空白
                                label_text = re.sub(r'\{\{[^}]*\}\}', '', all_text).strip()
                                
                                # 如果得到了有效的标签文本，使用它
                                if label_text and label_text != "":
                                    # 保持原有格式，不添加额外的标点
                                    pass
                                else:
                                    label_text = ""
                        except Exception as e:
                            logger.debug(f"解析标签文本时出错: {str(e)}")
                        
                        # 方法2：如果方法1失败，使用简单的文本提取
                        if not label_text:
                            # 直接提取HTML中的所有文本（去除标签）
                            text_only = re.sub(r'<[^>]+>', '', between_html)
                            if text_only:
                                label_text = re.sub(r'\{\{[^}]*\}\}', '', text_only).strip()
                    
                    # 如果是第一个占位符或无法获取前面的标签，尝试特殊处理
                    if not label_text:
                        # 对于第一个占位符，需要特别处理
                        if i == 0:
                            # 尝试从段落开始到第一个占位符之间提取文本
                            first_placeholder_pos = placeholder['position']
                            before_first_placeholder = element_html[:first_placeholder_pos]
                            
                            # 方法1：直接提取占位符前面的所有span内容
                            try:
                                # 创建临时元素来解析HTML
                                temp_element = fromstring(f"<div>{before_first_placeholder}</div>")
                                
                                # 获取所有文本内容（包括span内的文本）
                                all_text = temp_element.text_content()
                                if all_text:
                                    # 清理文本：去除占位符标记和多余空白
                                    label_text = re.sub(r'\{\{[^}]*\}\}', '', all_text).strip()
                                    
                                    # 如果得到了有效的标签文本，使用它
                                    if label_text and label_text != "":
                                        # 保持原有格式，不添加额外的标点
                                        pass
                                    else:
                                        label_text = ""
                            except Exception as e:
                                logger.debug(f"解析标签文本时出错: {str(e)}")
                            
                            # 方法2：如果方法1失败，使用简单的文本提取
                            if not label_text:
                                # 直接提取HTML中的所有文本（去除标签）
                                text_only = re.sub(r'<[^>]+>', '', before_first_placeholder)
                                if text_only:
                                    label_text = re.sub(r'\{\{[^}]*\}\}', '', text_only).strip()
                    
                    # 创建input组件，传递完整的占位符文本用于提取currentId
                    placeholder_text = placeholder['span'].text_content().strip()
                    input_item = self._create_input(placeholder_text, label_text)
                    # 设置变量名
                    input_item["options"]["name"] = var_name
                    # 设置对齐方式
                    input_item["options"]["labelAlign"] = text_align
                    results.append(input_item)
        else:
            # 检查段落中是否有图片元素
            images = element.xpath('.//img')
            if images:
                # 有图片元素，需要智能处理混合内容（文本+图片）
                # 使用新的混合内容处理方法
                mixed_widgets = self._process_mixed_content_paragraph(element, text_align, background_color)
                results.extend(mixed_widgets)
            else:
                # 没有图片，按原有逻辑处理文本内容
                text = self._get_plain_text(element)
                
                # 检查是否包含复选框符号□
                if '□' in text:
                    checkbox_widgets = self._process_checkbox_text(text, text_align)
                    if checkbox_widgets:
                        results.extend(checkbox_widgets)
                        return results
                
                if text.strip():
                    # 创建静态文本组件
                    text_id = self.generate_id()
                    text_item = {
                        "key": text_id,
                        "type": "static-text",
                        "icon": "static-text",
                        "formItemFlag": False,
                        "options": {
                            "name": f"statictext_{text_id}",
                            "columnWidth": "200px",
                            "hidden": False,
                            "textContent": text,
                            "textAlign": text_align,
                            "fontSize": "13px",
                            "fontWeight": "normal",
                            "backgroundColor": background_color,
                            "preWrap": True,
                            "customClass": [],
                            "onCreated": "",
                            "onMounted": "",
                            "label": "static-text"
                        },
                        "id": f"statictext_{text_id}"
                    }
                    results.append(text_item)
        
        return results
    
    def _process_mixed_content_paragraph(self, element, text_align='center', background_color=''):
        """处理包含图片和文本的混合内容段落
        
        Args:
            element: HTML段落元素
            text_align: 文本对齐方式
            background_color: 背景色
            
        Returns:
            list: vForm组件列表
        """
        results = []
        
        # 获取段落的所有子节点（包括文本节点和元素节点）
        # 使用lxml的方式来遍历所有节点
        from lxml import etree
        
        # 构建节点序列，区分文本和元素
        nodes = []
        
        # 处理段落的直接文本内容
        if element.text:
            nodes.append(('text', element.text))
        
        # 遍历所有子元素
        for child in element:
            # 如果是图片元素
            if child.tag.lower() == 'img':
                nodes.append(('img', child))
            else:
                # 其他元素（主要是span），提取其文本内容
                child_text = self._get_plain_text(child)
                if child_text.strip():
                    nodes.append(('text', child_text))
            
            # 处理子元素后的尾随文本
            if child.tail:
                nodes.append(('text', child.tail))
        
        # 合并连续的文本节点，分离图片节点
        merged_nodes = []
        current_text = ""
        
        for node_type, node_content in nodes:
            if node_type == 'text':
                # 累积文本内容，之后会统一格式化
                current_text += node_content
            elif node_type == 'img':
                # 如果有累积的文本，先添加文本节点
                if current_text.strip():
                    merged_nodes.append(('text', current_text.strip()))
                    current_text = ""
                # 添加图片节点
                merged_nodes.append(('img', node_content))
        
        # 处理最后的文本
        if current_text.strip():
            merged_nodes.append(('text', current_text.strip()))
        
        # 创建组件
        for node_type, node_content in merged_nodes:
            if node_type == 'text':
                # 对文本内容进行最终格式化
                formatted_text = re.sub(r'\s+', ' ', node_content).strip()
                
                # 创建文本组件
                text_id = self.generate_id()
                text_widget = {
                    "key": text_id,
                    "type": "static-text",
                    "icon": "static-text",
                    "formItemFlag": False,
                    "options": {
                        "name": f"statictext_{text_id}",
                        "columnWidth": "200px",
                        "hidden": False,
                        "textContent": formatted_text,
                        "textAlign": text_align,
                        "fontSize": "13px",
                        "fontWeight": "normal",
                        "backgroundColor": background_color,
                        "preWrap": True,
                        "customClass": [],
                        "onCreated": "",
                        "onMounted": "",
                        "label": "static-text"
                    },
                    "id": f"statictext_{text_id}"
                }
                results.append(text_widget)
            elif node_type == 'img':
                # 创建图片组件
                img_src = node_content.get('src', '')
                if img_src:
                    try:
                        img_widget = self._create_image(img_src, node_content)
                        results.append(img_widget)
                    except Exception as e:
                        logger.error(f"处理图片时出错: {str(e)}")
                        # 如果图片处理失败，跳过这个图片
                        pass
        
        return results

    # ...existing code...
    
    def _create_image(self, src, element=None):
        """创建vForm图片组件
        Args:
            src: 图片路径
            element: 原始HTML图片元素（可选）
        Returns:
            dict: vForm图片组件
        """
        # 默认图片尺寸
        img_width = 100
        img_height = 100
        
        # 如果提供了原始元素，直接获取width和height属性
        if element is not None:
            # 获取宽度属性
            width_attr = element.get('width')
            if width_attr is not None and width_attr != "":
                try:
                    # 处理纯数字
                    if width_attr.isdigit():
                        img_width = int(width_attr)
                except Exception:
                    pass
            
            # 获取高度属性
            height_attr = element.get('height')
            if height_attr is not None and height_attr != "":
                try:
                    # 处理纯数字
                    if height_attr.isdigit():
                        img_height = int(height_attr)
                except Exception:
                    pass
        
        # 获取图片的实际src
        if element is not None:
            src = element.get('src', src)
            
        # 处理图片URL
        if src:
            # 检查是否是base64编码
            if src.startswith('data:'):
                # 转换为文件流并上传
                stream = base64_to_file_stream(src)
                if stream:
                    img_url = upload_file(stream)
                    src = img_url

        img_id = self.generate_id()
        result = {
            "key": img_id,
            "type": "imgShow",
            "icon": "picture-upload-field",
            "alias": "静态图片",
            "formItemFlag": True,
            "options": {
                "name": f"imgShow_{img_id}",
                "imgUrl": src,
                "imgUploadUrl": src,
                "imglabel": "",
                "imglabelAlign": "center",
                "imglabelWidth": 0,
                "imglabelHidden": False,
                "desPostion": "left",
                "imgWidth": img_width,
                "imgHeight": img_height,
                "marginTop": 20,
                "marginRight": 0,
                "marginBottom": 20,
                "marginLeft": 80,
                "description": "图片描述",
                "label": ""
            },
            "id": f"imgShow_{img_id}"
        }
        
        return result
    
    def _process_checkbox_text(self, text, text_align='left'):
        """处理包含□复选框的文本
        
        Args:
            text: 包含□的文本
            text_align: 文本对齐方式
            
        Returns:
            list: 复选框和静态文本组件的列表
        """
        # 使用正则表达式匹配"□"后面跟着文字的模式
        matches = re.findall(r'□\s*([^\□]+)', text)
        
        if not matches:
            return None

        # 生成复选框选项
        options = [{"label": match.strip(), "value": i + 1} for i, match in enumerate(matches) if match.strip()]
        
        if not options:
            return None
        
        # 从原始文本中移除复选框部分
        cleaned_text = text
        for match in matches:
            # 移除"□"和后面的选项文本
            pattern = f'□\\s*{re.escape(match)}'
            cleaned_text = re.sub(pattern, '', cleaned_text)
        
        # 清理文本中可能剩余的多余空白
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
            
        # 创建栅格布局
        grid_id = self.generate_id()
        text_id = self.generate_id()
        grid_col_1_id = self.generate_id()
        grid_col_2_id = self.generate_id()
        grid = {
            "key": grid_id,
            "type": "grid",
            "alias": "column-2-grid",
            "category": "container",
            "icon": "column-2-grid",
            "cols": [
                # 第一列：静态文本（不包含复选框选项）
                {
                    "type": "grid-col",
                    "category": "container",
                    "icon": "grid-col",
                    "internal": True,
                    "widgetList": [
                        {
                            "key": text_id,
                            "type": "static-text",
                            "icon": "static-text",
                            "formItemFlag": False,
                            "options": {
                                "name": f"statictext_{text_id}",
                                "columnWidth": "200px",
                                "hidden": False,
                                "textContent": cleaned_text,
                                "textAlign": text_align,
                                "fontSize": "13px",
                                "preWrap": True,
                                "customClass": [],
                                "label": "static-text"
                            },
                            "id": f"statictext_{text_id}"
                        }
                    ],
                    "options": {
                        "name": f"gridCol_{grid_col_1_id}",
                        "hidden": False,
                        "border": False,
                        "span": 2,
                        "offset": 0,
                        "push": 0,
                        "pull": 0,
                        "responsive": False,
                        "md": 12,
                        "sm": 12,
                        "xs": 12,
                        "customClass": []
                    },
                    "id": f"grid-col-{grid_col_1_id}"
                },
                # 第二列：复选框
                {
                    "type": "grid-col",
                    "category": "container",
                    "icon": "grid-col",
                    "internal": True,
                    "widgetList": [
                        self._create_checkbox(options)
                    ],
                    "options": {
                        "name": f"gridCol_{grid_col_2_id}",
                        "hidden": False,
                        "border": False,
                        "span": 22,
                        "offset": 0,
                        "push": 0,
                        "pull": 0,
                        "responsive": False,
                        "md": 12,
                        "sm": 12,
                        "xs": 12,
                        "customClass": ""
                    },
                    "id": f"grid-col-{grid_col_2_id}"
                }
            ],
            "options": {
                "name": f"grid_{grid_id}",
                "hidden": False,
                "gutter": 12,
                "colHeight": None,
                "customClass": []
            },
            "id": f"grid_{grid_id}"
        }
        
        # 如果没有剩余文本，隐藏第一列
        if not cleaned_text:
            grid["cols"][0]["options"]["hidden"] = True
        
        return [grid]
    
    def _create_input(self, var_name, label=None):
        """创建vForm输入框组件
        
        Args:
            var_name: 变量名
            label: 输入框标签，如果为None则使用变量名
            
        Returns:
            dict: vForm输入框组件
        """
        if label is None:
            clean_label = ""
        else:
            # 清除标签中的占位符标记
            clean_label = re.sub(r'\{\{[^}]*\}\}', '', label).strip()
        
        # 从占位符中提取计数器值，但不在组件中使用currentId
        if var_name:
            # 使用正则表达式提取占位符中的数字
            placeholder_match = re.search(r'\{\{\s*(\d+)\s*\}\}', var_name)
            # 这里可以添加其他基于占位符的逻辑，但currentId不应该在组件中
        
        # 生成ID，确保统一使用
        input_id = self.generate_id()
        return {
            "key": input_id,
            "type": "input",
            "alias": "",
            "icon": "text-field",
            "formItemFlag": True,
            "options": {
                "name": var_name,
                "label": clean_label,
                "labelAlign": "",
                "type": "text",
                "defaultValue": "",
                "placeholder": "请输入",
                "autoFullWidth": True,
                "columnWidth": "200px",
                "size": "",
                "labelWidth": None,
                "labelHidden": False,
                "readonly": False,
                "disabled": False,
                "hidden": False,
                "clearable": True,
                "required": False,
                "customClass": []
            },
            "id": f"input_{input_id}"
        }
    
    def _create_checkbox(self, options):
        """创建vForm复选框组件
        
        Args:
            options: 选项列表 [{"label": "选项1", "value": 1}, ...]
            
        Returns:
            dict: vForm复选框组件
        """
        checkbox_id = self.generate_id()
        return {
            "key": checkbox_id,
            "type": "checkbox",
            "icon": "checkbox-field",
            "formItemFlag": True,
            "options": {
                "name": f"{checkbox_id}",
                "keyNameEnabled": False,
                "keyName": "",
                "label": "",
                "labelAlign": "",
                "defaultValue": [],
                "columnWidth": "200px",
                "size": "",
                "displayStyle": "inline",
                "buttonStyle": False,
                "border": False,
                "labelWidth": None,
                "labelHidden": False,
                "labelWrap": False,
                "disabled": False,
                "hidden": False,
                "dsEnabled": False,
                "dsName": "",
                "dataSetName": "",
                "showUnchecked": False,
                "labelKey": "label",
                "valueKey": "value",
                "optionValueType": "",
                "optionItems": options,
                "required": False,
                "requiredHint": "",
                "validation": "",
                "validationHint": "",
                "customClass": [],
                "labelIconClass": None,
                "labelIconPosition": "rear",
                "labelTooltip": None,
                "onCreated": "",
                "onMounted": "",
                "onChange": "",
                "onValidate": ""
            },
            "id": f"checkbox_{checkbox_id}"
        }
    
    def _create_empty_table(self):
        """创建空表格
        
        Returns:
            dict: 空的vForm表格组件
        """
        empty_table_id = self.generate_id()
        return {
            "key": empty_table_id,
            "type": "table",
            "category": "container",
            "icon": "table",
            "rows": [],
            "options": {
                "name": f"table-{empty_table_id}",
                "hidden": False,
                "customClass": []
            },
            "id": f"table-{empty_table_id}"
        }