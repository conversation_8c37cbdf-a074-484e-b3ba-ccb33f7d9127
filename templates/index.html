<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>广安电气Word文档解析器</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap"
        rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/jsoneditor/9.10.2/jsoneditor.min.css" rel="stylesheet"
        type="text/css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jsoneditor/9.10.2/jsoneditor.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.44.0/min/vs/loader.js"></script>
    <style>
        :root {
            --primary-color: #2196F3;
            --primary-hover: #1976D2;
            --success-color: #f44336;
            --success-hover: #d32f2f;
            --text-color: #333;
            --bg-color: #f5f5f5;
            --card-bg: #ffffff;
            --border-color: #e0e0e0;
            --dark-bg: #1e1e1e;
            --dark-text: #d4d4d4;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            background-color: var(--bg-color);
            color: var(--text-color);
            min-height: 100vh;
            padding: 40px 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: var(--card-bg);
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .container:hover {
            transform: translateY(-5px);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        h1 {
            color: var(--primary-color);
            font-weight: 700;
            font-size: 2.5rem;
            letter-spacing: 1px;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 20px;
        }

        .upload-section {
            text-align: center;
            padding: 40px;
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            background-color: rgba(33, 150, 243, 0.05);
            position: relative;
        }

        .upload-section:hover {
            border-color: var(--primary-color);
            background-color: rgba(33, 150, 243, 0.1);
        }

        .upload-icon {
            font-size: 40px;
            color: var(--primary-color);
            margin-right: 15px;
            vertical-align: middle;
        }

        #fileInputHtml {
            display: none;
        }

        .upload-btn {
            background-color: var(--primary-color);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            letter-spacing: 1px;
            display: inline-block;
        }

        .upload-container {
            display: inline-flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .upload-btn:hover {
            background-color: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .upload-section p {
            margin-top: 20px;
            color: #666;
            font-size: 14px;
        }

        .file-info {
            margin-top: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            color: var(--primary-color);
            font-size: 16px;
        }

        .file-info i {
            color: var(--primary-color);
            font-size: 20px;
        }

        .download-btn {
            background-color: var(--success-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .download-btn:hover {
            background-color: var(--success-hover);
            transform: translateY(-2px);
        }

        .json-display {
            background-color: var(--dark-bg);
            padding: 30px;
            border-radius: 12px;
            margin-top: 30px;
            position: relative;
            border: 1px solid #333;
            transition: all 0.3s ease;
            height: 600px;
        }

        .json-container {
            height: 100%;
            width: 100%;
            position: relative;
            border-radius: 8px;
            overflow: hidden;
        }

        #monaco-editor {
            width: 100%;
            height: 100%;
        }

        .jsoneditor {
            border: none !important;
        }

        /* 标签页样式 */
        .tabs, .result-tabs {
            margin-bottom: 30px;
        }

        .tab-header {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .tab-item {
            padding: 10px 20px;
            cursor: pointer;
            color: #666;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
        }

        .tab-item:hover {
            color: var(--primary-color);
        }

        .tab-item.active {
            color: var(--primary-color);
            font-weight: 600;
        }

        .tab-item.active:after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--primary-color);
        }

        .tab-content {
            position: relative;
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
        }

        /* HTML预览区域 */
        .html-preview {
            background-color: white;
            padding: 30px;
            border-radius: 12px;
            margin-top: 30px;
            position: relative;
            border: 1px solid #eee;
            transition: all 0.3s ease;
            height: 600px;
            overflow: auto;
        }

        #html-container {
            width: 100%;
            height: 100%;
        }

        /* 按钮样式 */
        .convert-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-left: 15px;
        }

        .convert-btn:hover {
            background-color: var(--primary-hover);
            transform: translateY(-2px);
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            gap: 15px;
        }

        .action-btn {
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn:hover {
            background-color: var(--primary-hover);
            transform: translateY(-2px);
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: none;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            z-index: 1000;
            color: white;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .overlay p {
            font-size: 18px;
            animation: fadeIn 0.5s ease-out;
        }

        /* 复制按钮样式 */
        #copyBtn {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 100;
            padding: 8px 12px;
            font-size: 12px;
            opacity: 0.8;
            transition: opacity 0.3s ease;
            background-color: var(--success-color);
        }

        #copyBtn:hover {
            opacity: 1;
            transform: translateY(-2px);
            background-color: var(--success-hover);
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>广安电气Word文档解析器</h1>
            <p class="subtitle">上传Word文档，直接转换为vForm JSON</p>
        </div>
        
        <div class="upload-section">
            <div class="upload-container">
                <i class="fas fa-file-word upload-icon"></i>
                <label for="fileInputHtml" class="upload-btn">选择Word文档</label>
                <input type="file" id="fileInputHtml" accept=".doc,.docx">
            </div>
            <p align="center">支持.docx格式的Word文档</p>
            <div class="file-info" id="fileInfoHtml" style="display: none;">
                <i class="fas fa-file-alt"></i>
                <span id="fileNameHtml"></span>
                <button class="convert-btn" id="convertHtmlBtn" style="display: none;">开始转换</button>
                <button class="download-btn" id="downloadPlaceholderBtn">
                    <i class="fas fa-download"></i>
                    下载
                </button>
            </div>
        </div>
        
        <div id="result-section" style="display: none;">
                        <div class="json-display">
                            <div class="json-container" id="json-container"></div>
                            <button id="copyBtn" class="action-btn"><i class="fas fa-copy"></i> 复制JSON</button>
                        </div>
                        <div class="action-buttons">
                            <!-- Download button removed -->
            </div>
        </div>
    </div>

    <div class="overlay" id="overlay">
        <div class="loading-spinner"></div>
        <p>正在处理文件...</p>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let editor = null;
            let monacoEditor = null;
            let currentResult = null;
            let currentHtml = null;
            let currentFile = null;
            
            // 初始化Monaco编辑器
            require.config({
                paths: {
                    'vs': 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.44.0/min/vs'
                }
            });
            
            require(['vs/editor/editor.main'], function() {
                monacoEditor = monaco.editor.create(document.getElementById('json-container'), {
                    value: "",
                    language: 'json',
                    theme: 'vs-dark',
                    automaticLayout: true,
                    minimap: { enabled: true },
                    scrollBeyondLastLine: false,
                    readOnly: true
                });
            });
            
            // 标签页切换
            document.querySelectorAll('.tab-item').forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.id;
                    const paneId = 'pane-' + tabId.split('-')[1];
                    
                    // 选择对应的标签组
                    const tabGroup = this.closest('.tab-header').parentElement;
                    
                    // 清除当前标签组的所有激活状态
                    tabGroup.querySelectorAll('.tab-item').forEach(t => t.classList.remove('active'));
                    tabGroup.querySelectorAll('.tab-pane').forEach(p => p.classList.remove('active'));
                    
                    // 激活点击的标签和对应的内容面板
                    this.classList.add('active');
                    tabGroup.querySelector('#' + paneId).classList.add('active');
                });
            });
            
            // 文件上传处理
            const fileInputHtml = document.getElementById('fileInputHtml');
            const fileInfoHtml = document.getElementById('fileInfoHtml');
            const fileNameHtml = document.getElementById('fileNameHtml');
            const convertHtmlBtn = document.getElementById('convertHtmlBtn');
            const downloadPlaceholderBtn = document.getElementById('downloadPlaceholderBtn');
            
            fileInputHtml.addEventListener('change', function(e) {
                if (this.files && this.files[0]) {
                    currentFile = this.files[0];
                    fileNameHtml.textContent = this.files[0].name;
                    fileInfoHtml.style.display = 'flex';
                    
                    // 自动开始转换
                    convertFile(this.files[0]);
                }
            });
            
            // 提取转换逻辑为独立函数，以便重用
            async function convertFile(file) {
                if (!file) {
                    alert('请先选择文件');
                    return;
                }
                
                const formData = new FormData();
                formData.append('file', file);
                
                // 显示加载覆盖层
                document.getElementById('overlay').style.display = 'flex';
                
                try {
                    const response = await fetch('/convert_via_html', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.json();
                    if (result.status === 200) {
                        currentResult = result.data;
                        
                        // 更新Monaco编辑器内容
                        if (monacoEditor) {
                            monacoEditor.setValue(JSON.stringify(result.data, null, 2));
                        }
                        
                        // 显示结果区域
                        document.getElementById('result-section').style.display = 'block';
                    } else {
                        alert('处理失败: ' + result.message);
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('发生错误：' + error.message);
                } finally {
                    // 隐藏加载覆盖层
                    document.getElementById('overlay').style.display = 'none';
                }
            }
            
            // 保留原始转换按钮功能，以防需要重新转换
            convertHtmlBtn.addEventListener('click', function() {
                if (currentFile) {
                    convertFile(currentFile);
                }
            });
            
            // 下载占位符文件
            downloadPlaceholderBtn.addEventListener('click', async function() {
                if (!currentFile) {
                    alert('请先选择文件');
                    return;
                }
                
                const formData = new FormData();
                formData.append('file', currentFile);
                
                // 显示加载覆盖层
                document.getElementById('overlay').style.display = 'flex';
                
                try {
                    const response = await fetch('/download', {
                        method: 'POST',
                        body: formData
                    });
                    
                    // 检查返回的内容类型
                    const contentType = response.headers.get('content-type');
                    
                    if (contentType && contentType.includes('application/json')) {
                        // 如果返回的是JSON，说明有错误
                        const errorResult = await response.json();
                        alert('处理失败: ' + errorResult.message);
                    } else {
                        // 如果是文件，创建并点击下载链接
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.style.display = 'none';
                        a.href = url;
                        
                        // 设置下载文件名
                        const contentDisposition = response.headers.get('content-disposition');
                        let filename = 'document_with_placeholders.docx';
                        if (contentDisposition) {
                            const filenameMatch = contentDisposition.match(/filename=(.*)/i);
                            if (filenameMatch && filenameMatch[1]) {
                                filename = filenameMatch[1].trim().replace(/"/g, '');
                            }
                        }
                        
                        a.download = decodeURIComponent(filename);
                        document.body.appendChild(a);
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                    }
                } catch (error) {
                    console.error('Error:', error);
                    alert('发生错误：' + error.message);
                } finally {
                    // 隐藏加载覆盖层
                    document.getElementById('overlay').style.display = 'none';
                }
            });
            
            // 复制JSON按钮
            document.getElementById('copyBtn').addEventListener('click', function() {
                if (currentResult) {
                    const copyBtn = this;
                    const originalText = copyBtn.innerHTML;
                    const jsonText = JSON.stringify(currentResult, null, 2);
                    
                    // 使用兼容性更好的复制方法
                    function fallbackCopyTextToClipboard(text) {
                        // 创建一个临时textarea元素
                        const textArea = document.createElement("textarea");
                        textArea.value = text;
                        
                        // 设置样式使其不可见
                        textArea.style.top = "0";
                        textArea.style.left = "0";
                        textArea.style.position = "fixed";
                        textArea.style.opacity = "0";
                        
                        document.body.appendChild(textArea);
                        textArea.focus();
                        textArea.select();
                        
                        let success = false;
                        try {
                            // 执行复制命令
                            success = document.execCommand('copy');
                        } catch (err) {
                            console.error('复制失败:', err);
                        }
                        
                        document.body.removeChild(textArea);
                        return success;
                    }
                    
                    // 尝试使用新API，如果不支持则使用兼容方法
                    let success = false;
                    if (navigator.clipboard && navigator.clipboard.writeText) {
                        navigator.clipboard.writeText(jsonText)
                            .then(() => {
                                showCopiedSuccess();
                            })
                            .catch(err => {
                                console.error('Clipboard API失败, 使用备用方法:', err);
                                success = fallbackCopyTextToClipboard(jsonText);
                                if (success) {
                                    showCopiedSuccess();
                                } else {
                                    alert('复制失败，请手动复制');
                                }
                            });
                    } else {
                        // 不支持Clipboard API，使用备用方法
                        success = fallbackCopyTextToClipboard(jsonText);
                        if (success) {
                            showCopiedSuccess();
                        } else {
                            alert('复制失败，请手动复制');
                        }
                    }
                    
                    // 显示复制成功的反馈
                    function showCopiedSuccess() {
                        copyBtn.innerHTML = '<i class="fas fa-check"></i> 已复制';
                        copyBtn.style.backgroundColor = '#4CAF50'; // Green color
                        
                        // Reset button after 2 seconds
                        setTimeout(() => {
                            copyBtn.innerHTML = originalText;
                            copyBtn.style.backgroundColor = '';
                        }, 2000);
                    }
                }
            });
        });
    </script>
</body>

</html>