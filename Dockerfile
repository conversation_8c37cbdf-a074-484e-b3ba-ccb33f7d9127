FROM ubuntu:22.04

# 安装Python
RUN apt-get update -y && \
    apt-get install -y python3-pip python3-dev && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 复制依赖文件并安装
COPY ./requirements.txt /app/
RUN pip3 install --no-cache-dir -r /app/requirements.txt

# 复制项目代码
COPY . /app/

# 容器启动时通过环境变量设置的FLASK_ENV来选择加载哪个配置文件
# 默认使用开发环境配置
ENV FLASK_ENV=development

# 暴露端口
EXPOSE 5001

# 容器启动命令
ENTRYPOINT ["python3"]
CMD ["app.py"]