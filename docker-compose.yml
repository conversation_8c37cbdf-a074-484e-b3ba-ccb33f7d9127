version: '3'

services:
  app:
    build: .
    ports:
      - "5001:5001"
    volumes:
      - ./.env:/app/.env:ro  # 挂载默认.env文件
    environment:
      - FLASK_ENV=development  # 默认环境，可被覆盖
    networks:
      - app-network

  # 生产环境配置
  app-prod:
    build: .
    ports:
      - "5001:5001"  # 使用8080端口
    volumes:
      - ./.env.production:/app/.env:ro  # 挂载生产环境配置为默认.env
    environment:
      - FLASK_ENV=production
    networks:
      - app-network
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G

  # 测试环境配置
  app-test:
    build: .
    ports:
      - "5001:5001"
    volumes:
      - ./.env.testing:/app/.env:ro  # 挂载测试环境配置为默认.env
    environment:
      - FLASK_ENV=testing
    networks:
      - app-network

networks:
  app-network:
    driver: bridge 